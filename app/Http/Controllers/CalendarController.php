<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Field;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalendarController extends Controller
{
    /**
     * Display the calendar interface
     */
    public function index()
    {
        $fields = Field::active()->orderBy('name')->get();

        return view('calendar.index', compact('fields'));
    }

    /**
     * Get calendar events (AJAX endpoint for FullCalendar)
     */
    public function events(Request $request)
    {
        try {
            $start = $request->start;
            $end = $request->end;
            $fieldId = $request->field_id;

            $query = Booking::with(['field', 'user'])
                ->where('booking_date', '>=', Carbon::parse($start)->toDateString())
                ->where('booking_date', '<=', Carbon::parse($end)->toDateString())
                ->whereIn('status', ['Pending', 'Confirmed', 'Completed']);

            // Filter by field if specified
            if ($fieldId) {
                $query->where('field_id', $fieldId);
            }

            // Filter by user role
            if (auth()->user()->isClient()) {
                // Clients can only see their own bookings
                $query->where('user_id', auth()->id());
            } elseif (auth()->user()->isEmployee()) {
                // Employees can see all confirmed bookings + their own
                $query->where(function ($q) {
                    $q->where('status', 'Confirmed')
                        ->orWhere('user_id', auth()->id());
                });
            }
            // Admins can see all bookings (no additional filter)

            $bookings = $query->get();

            $events = [];
            foreach ($bookings as $booking) {
                // Create datetime strings for FullCalendar
                $startTime = $booking->start_time;
                $endTime = $booking->end_time;

                // Ensure we have proper time format
                if (! is_string($startTime)) {
                    $startTime = $startTime->format('H:i');
                }
                if (! is_string($endTime)) {
                    $endTime = $endTime->format('H:i');
                }

                $startDateTime = $booking->booking_date->format('Y-m-d').'T'.$startTime.':00';
                $endDateTime = $booking->booking_date->format('Y-m-d').'T'.$endTime.':00';

                // Determine event color based on status
                $color = match ($booking->status) {
                    'Pending' => '#fbbf24', // yellow
                    'Confirmed' => '#10b981', // green
                    'Cancelled' => '#ef4444', // red
                    'Completed' => '#3b82f6', // blue
                    default => '#6b7280', // gray
                };

                // Determine if user can edit this booking
                $canEdit = auth()->user()->isAdmin() ||
                          ($booking->user_id === auth()->id() && $booking->canBeCancelled());

                $events[] = [
                    'id' => $booking->id,
                    'title' => $booking->field->name.' - '.$booking->customer_display_name,
                    'start' => $startDateTime,
                    'end' => $endDateTime,
                    'color' => $color,
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'booking_id' => $booking->id,
                        'field_id' => $booking->field_id,
                        'field_name' => $booking->field->name,
                        'customer_name' => $booking->customer_display_name,
                        'status' => $booking->status,
                        'total_cost' => number_format($booking->total_cost, 2),
                        'duration' => $booking->duration_hours,
                        'can_edit' => $canEdit,
                        'special_requests' => $booking->special_requests,
                    ],
                    'url' => route('bookings.show', $booking),
                ];
            }

            return response()->json($events);

        } catch (\Exception $e) {
            Log::error('Calendar events error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json(['error' => 'Failed to fetch calendar events'], 500);
        }
    }

    /**
     * Check availability for a specific time slot (AJAX endpoint)
     */
    public function checkAvailability(Request $request)
    {
        $fieldId = $request->field_id;
        $date = $request->date;
        $startTime = $request->start_time;
        $duration = $request->duration;

        if (! $fieldId || ! $date || ! $startTime || ! $duration) {
            return response()->json(['available' => false, 'message' => 'Missing required parameters']);
        }

        $field = Field::find($fieldId);
        if (! $field) {
            return response()->json(['available' => false, 'message' => 'Field not found']);
        }

        // Calculate end time
        $startTimeCarbon = Carbon::createFromFormat('H:i', $startTime);
        $endTimeCarbon = $startTimeCarbon->copy()->addHours((int) $duration);
        $endTime = $endTimeCarbon->format('H:i');

        // Check business hours
        if ($startTimeCarbon->hour < 8 || $endTimeCarbon->hour > 22) {
            return response()->json([
                'available' => false,
                'message' => 'Bookings must be between 8:00 AM and 10:00 PM',
            ]);
        }

        // Check if date is in the past
        if (Carbon::parse($date)->isPast()) {
            return response()->json([
                'available' => false,
                'message' => 'Cannot book in the past',
            ]);
        }

        // Check field availability
        $available = $field->isAvailable($date, $startTime, $endTime);

        if ($available) {
            $totalCost = $field->hourly_rate * (int) $duration;

            return response()->json([
                'available' => true,
                'message' => 'Time slot is available',
                'total_cost' => number_format($totalCost, 2),
                'hourly_rate' => number_format($field->hourly_rate, 2),
            ]);
        } else {
            return response()->json([
                'available' => false,
                'message' => 'Time slot is not available',
            ]);
        }
    }

    /**
     * Get available time slots for a specific date and field
     */
    public function getAvailableSlots(Request $request)
    {
        $fieldId = $request->field_id;
        $date = $request->date;

        if (! $fieldId || ! $date) {
            return response()->json(['slots' => []]);
        }

        $field = Field::find($fieldId);
        if (! $field) {
            return response()->json(['slots' => []]);
        }

        // Get existing bookings for the date
        $existingBookings = Booking::where('field_id', $fieldId)
            ->where('booking_date', $date)
            ->whereIn('status', ['Pending', 'Confirmed'])
            ->orderBy('start_time')
            ->get();

        // Generate available slots (8 AM to 10 PM, 1-hour increments)
        $availableSlots = [];
        for ($hour = 8; $hour < 22; $hour++) {
            $timeSlot = sprintf('%02d:00', $hour);
            $endTime = sprintf('%02d:00', $hour + 1);

            // Check if this slot conflicts with existing bookings
            $isAvailable = true;
            foreach ($existingBookings as $booking) {
                $bookingStart = Carbon::parse($booking->start_time);
                $bookingEnd = Carbon::parse($booking->end_time);
                $slotStart = Carbon::createFromFormat('H:i', $timeSlot);
                $slotEnd = Carbon::createFromFormat('H:i', $endTime);

                if ($slotStart->between($bookingStart, $bookingEnd, false) ||
                    $slotEnd->between($bookingStart, $bookingEnd, false) ||
                    ($slotStart->lte($bookingStart) && $slotEnd->gte($bookingEnd))) {
                    $isAvailable = false;
                    break;
                }
            }

            if ($isAvailable) {
                $availableSlots[] = [
                    'time' => $timeSlot,
                    'display' => Carbon::createFromFormat('H:i', $timeSlot)->format('g:i A'),
                ];
            }
        }

        return response()->json(['slots' => $availableSlots]);
    }

    // pa calendar:
    public function updateBookingDate(Request $request, $id)
    {
        $request->validate([
            'start' => 'required|date',
            'end' => 'nullable|date',
        ]);

        $booking = Booking::findOrFail($id);

        // Check permission
        if (! auth()->user()->isAdmin() && auth()->id() !== $booking->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            $startDateTime = Carbon::parse($request->start);
            $endDateTime = Carbon::parse($request->end);

            // Update booking_date, start_time, end_time
            $booking->booking_date = $startDateTime->toDateString();
            $booking->start_time = $startDateTime->format('H:i');
            $booking->end_time = $endDateTime->format('H:i');
            $booking->save();

            DB::commit();

            return response()->json(['message' => 'Booking updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update booking from calendar drag', [
                'error' => $e->getMessage(),
            ]);

            return response()->json(['message' => 'Failed to update booking'], 500);
        }
    }
}
